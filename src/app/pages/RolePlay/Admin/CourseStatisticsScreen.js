import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Button as AntButton,
  Space,
  Typography,
  Progress,
  Tag,
  Tooltip,
  Form as AntForm,
  message
} from "antd";
import {
  ArrowLeftOutlined,
  CalendarOutlined,
  UserOutlined,
  TrophyOutlined,
  PercentageOutlined,
  StarOutlined,
  ReloadOutlined,
  FilterOutlined,
  PlayCircleOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";

import Loading from "@app/component/Loading";
import { BUTTON } from "@constant";
import { getCourseStatistics } from "@services/RolePlay";

import "./CourseStatisticsScreen.scss";

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const CourseStatisticsScreen = ({ user }) => {
  const { t } = useTranslation();
  const { courseId } = useParams();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);
  const [statisticsData, setStatisticsData] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);
  const [formFilter] = AntForm.useForm();

  // Fetch course statistics
  const fetchCourseStatistics = async () => {
    setIsLoading(true);
    try {
      const params = {
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      };

      const data = await getCourseStatistics(courseId, params);
      setStatisticsData(data);
    } catch (error) {
      console.error("Error fetching course statistics:", error);
      message.error(t("FETCH_STATISTICS_ERROR") || "Có lỗi xảy ra khi tải dữ liệu thống kê");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (courseId) {
      fetchCourseStatistics();
    }
  }, [courseId, dateRange]);

  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
      formFilter.setFieldsValue({
        dateRange: dates
      });
    }
  };

  const handleRefresh = () => {
    fetchCourseStatistics();
  };

  const onSubmitFilter = (values) => {
    if (values.dateRange && values.dateRange.length === 2) {
      setDateRange(values.dateRange);
    }
  };

  const onClearFilter = () => {
    const defaultRange = [dayjs().subtract(30, 'day'), dayjs()];
    setDateRange(defaultRange);
    formFilter.setFieldsValue({
      dateRange: defaultRange
    });
  };

  // Initialize form with current date range
  useEffect(() => {
    formFilter.setFieldsValue({
      dateRange: dateRange
    });
  }, []);

  const handleGoBack = () => {
    navigate(-1);
  };

  // Top students table columns
  const topStudentsColumns = [
    {
      title: t("RANK"),
      width: 80,
      align: "center",
      render: (_, __, index) => (
        <div className="rank-cell">
          {index === 0 && <TrophyOutlined style={{ color: '#FFD700', marginRight: 4 }} />}
          {index === 1 && <TrophyOutlined style={{ color: '#C0C0C0', marginRight: 4 }} />}
          {index === 2 && <TrophyOutlined style={{ color: '#CD7F32', marginRight: 4 }} />}
          <Text strong>{index + 1}</Text>
        </div>
      )
    },
    {
      title: t("STUDENT_NAME"),
      dataIndex: "studentName",
      key: "studentName",
      render: (name) => (
        <div className="student-name">
          <UserOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          <Text>{name}</Text>
        </div>
      )
    },
    {
      title: t("AVERAGE_SCORE"),
      dataIndex: "averageScore",
      key: "averageScore",
      align: "center",
      render: (score) => {
        const numScore = parseFloat(score);
        const isExcellent = numScore >= 90;
        const isGood = numScore >= 80;
        const badgeClass = `score-badge ${isExcellent ? 'excellent' : isGood ? 'good' : 'average'}`;
        const tooltipTitle = isExcellent
          ? `🌟 Xuất sắc! Điểm trung bình: ${score}`
          : isGood
          ? `👍 Tốt! Điểm trung bình: ${score}`
          : `📈 Điểm trung bình: ${score}`;

        return (
          <Tooltip title={tooltipTitle} placement="top">
            <div className={badgeClass}>
              <div className="badge-icon">
                <StarOutlined />
              </div>
              <div className="badge-content">
                <span className="badge-number">{score}</span>
                <span className="badge-label">điểm</span>
              </div>
            </div>
          </Tooltip>
        );
      }
    },
    {
      title: t("TOTAL_SESSIONS"),
      dataIndex: "totalSessions",
      key: "totalSessions",
      align: "center",
      render: (count) => {
        const isHighValue = count >= 20;
        const badgeClass = `student-sessions-badge ${isHighValue ? 'high-value' : ''}`;
        const tooltipTitle = isHighValue
          ? `🔥 Học tập tích cực! ${count} phiên`
          : `${count} phiên học`;

        return (
          <Tooltip title={tooltipTitle} placement="top">
            <div className={badgeClass}>
              <div className="badge-icon">
                <PlayCircleOutlined />
              </div>
              <div className="badge-content">
                <span className="badge-number">{count}</span>
                <span className="badge-label">phiên</span>
              </div>
            </div>
          </Tooltip>
        );
      }
    },
    {
      title: t("HIGHEST_SCORE"),
      dataIndex: "highestScore",
      key: "highestScore",
      align: "center",
      render: (score) => {
        const numScore = parseFloat(score);
        const isPerfect = numScore >= 95;
        const badgeClass = `highest-score-badge ${isPerfect ? 'perfect' : ''}`;
        const tooltipTitle = isPerfect
          ? `🏆 Hoàn hảo! Điểm cao nhất: ${score}`
          : `🎯 Điểm cao nhất: ${score}`;

        return (
          <Tooltip title={tooltipTitle} placement="top">
            <div className={badgeClass}>
              <div className="badge-icon">
                <TrophyOutlined />
              </div>
              <div className="badge-content">
                <span className="badge-number">{score}</span>
                <span className="badge-label">điểm</span>
              </div>
            </div>
          </Tooltip>
        );
      }
    }
  ];

  if (!statisticsData && !isLoading) {
    return (
      <div className="course-statistics-container">
        <Card>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text>{t("NO_STATISTICS_DATA")}</Text>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <Loading active={isLoading} transparent>
      <div className="course-statistics-container">
        {/* Header */}
        <Card className="statistics-header-card">
          <div className="statistics-header">
            <div className="header-left">
              <div className="header-info">
                <Title level={2} className="statistics-title">
                  {t("COURSE_STATISTICS")}
                </Title>
                <Text className="course-name">
                  {statisticsData?.courseName}
                </Text>
              </div>
            </div>
            <div className="header-right">
              <AntButton
                type={BUTTON.GHOST_WHITE}
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                className="back-button"
                size="large"
              >
                {t("BACK")}
              </AntButton>
            </div>
          </div>
        </Card>

        {/* Filter Card */}
        <Card className="statistics-filter-card">
          <AntForm
            form={formFilter}
            layout="horizontal"
            size="large"
            className="form-filter"
            onFinish={onSubmitFilter}
          >
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={18}>
                <Row gutter={16} align="middle">
                  <Col xs={24} sm={12} md={10} lg={8}>
                    <AntForm.Item name="dateRange" className="filter-form-item">
                      <RangePicker
                        value={dateRange}
                        onChange={handleDateRangeChange}
                        format="DD/MM/YYYY"
                        suffixIcon={<CalendarOutlined />}
                        placeholder={[t("START_DATE"), t("END_DATE")]}
                        style={{ width: '100%' }}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} sm={12} md={6} lg={4}>
                    <div className="filter-info">
                      <Text type="secondary" className="filter-label">
                        <FilterOutlined style={{ marginRight: 4 }} />
                        {t("FILTER_PERIOD")}
                      </Text>
                    </div>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={6} className="filter-buttons-col">
                <div className="admin-filter-buttons">
                  <AntButton
                    type={BUTTON.GHOST_WHITE}
                    size="large"
                    onClick={onClearFilter}
                  >
                    {t("CLEAR_FILTER")}
                  </AntButton>
                  <AntButton
                    type={BUTTON.DEEP_NAVY}
                    size="large"
                    htmlType="submit"
                  >
                    {t("SEARCH_FILTER")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        {/* Statistics Overview */}
        <Card className="statistics-overview-card" title={t("OVERVIEW")}>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} lg={6}>
              <div className="overview-stat-card scenarios-card">
                <div className="stat-icon">
                  <CalendarOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{statisticsData?.totalScenarios}</div>
                  <div className="stat-label">{t("TOTAL_SCENARIOS")}</div>
                </div>
                <div className="stat-decoration"></div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <div className="overview-stat-card students-card">
                <div className="stat-icon">
                  <UserOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{statisticsData?.enrolledStudents}</div>
                  <div className="stat-label">{t("ENROLLED_STUDENTS")}</div>
                </div>
                <div className="stat-decoration"></div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <div className="overview-stat-card completed-card">
                <div className="stat-icon">
                  <TrophyOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{statisticsData?.completedStudents}</div>
                  <div className="stat-label">{t("COMPLETED_STUDENTS")}</div>
                </div>
                <div className="stat-decoration"></div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <div className="overview-stat-card score-card">
                <div className="stat-icon">
                  <StarOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{statisticsData?.averageScore}</div>
                  <div className="stat-label">{t("AVERAGE_SCORE")}</div>
                </div>
                <div className="stat-decoration"></div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Completion Rate */}
        <Card className="completion-rate-card" title={t("COMPLETION_RATE")}>
          <Row gutter={24} align="middle">
            <Col xs={24} md={12}>
              <div className="completion-progress">
                <Progress
                  type="circle"
                  percent={parseFloat(statisticsData?.completionRate || 0)}
                  format={(percent) => `${percent}%`}
                  size={200}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="completion-details">
                <div className="detail-item enrolled-item">
                  <div className="detail-icon">
                    <UserOutlined />
                  </div>
                  <div className="detail-content">
                    <div className="detail-label">{t("ENROLLED_STUDENTS")}</div>
                    <div className="detail-value">{statisticsData?.enrolledStudents}</div>
                  </div>
                </div>
                <div className="detail-item completed-item">
                  <div className="detail-icon">
                    <TrophyOutlined />
                  </div>
                  <div className="detail-content">
                    <div className="detail-label">{t("COMPLETED_STUDENTS")}</div>
                    <div className="detail-value">{statisticsData?.completedStudents}</div>
                  </div>
                </div>
                <div className="detail-item rate-item">
                  <div className="detail-icon">
                    <PercentageOutlined />
                  </div>
                  <div className="detail-content">
                    <div className="detail-label">{t("COMPLETION_RATE")}</div>
                    <div className="detail-value">{statisticsData?.completionRate}%</div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Top Students */}
        <Card className="top-students-card" title={t("TOP_STUDENTS")}>
          <Table
            columns={topStudentsColumns}
            dataSource={statisticsData?.topStudents || []}
            rowKey="studentId"
            pagination={false}
            size="middle"
          />
        </Card>
      </div>
    </Loading>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(CourseStatisticsScreen);
