// Keyframes for animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.statistics-overview-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .statistics-header-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .statistics-header {
      .header-left {
        .header-info {
          .statistics-title {
            margin: 0 0 4px 0;
            font-weight: 600;
            font-size: 24px;
          }

          .statistics-description {
            color: #666;
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
  }

  .statistics-filter-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .form-filter {
      .filter-form-item {
        margin-bottom: 0;
      }

      .filter-info {
        display: flex;
        align-items: center;
        height: 40px;
        padding-left: 8px;

        .filter-label {
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
        }
      }

      .filter-buttons-col {
        .filter-buttons {
          display: flex;
          gap: 12px;
          justify-content: flex-end;

          .ant-btn {
            min-width: 100px;
            height: 40px;
            border-radius: 6px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid #d9d9d9 !important;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
              border-color: #40a9ff !important;
            }

            &:focus {
              border-color: #1890ff !important;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }

            // Specific styling for different button types
            &.ant-btn-default {
              background-color: #fff;
              color: #595959;

              &:hover {
                background-color: #f5f5f5;
                color: #262626;
              }
            }

            &.ant-btn-primary {
              background-color: #1890ff;
              border-color: #1890ff !important;
              color: #fff;

              &:hover {
                background-color: #40a9ff;
                border-color: #40a9ff !important;
              }
            }
          }
        }
      }

      // Custom DatePicker styling
      .ant-picker {
        height: 40px;
        border-radius: 6px;
        border: 1px solid #d9d9d9;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-picker-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .ant-picker-input > input {
          font-size: 14px;
        }
      }
    }
  }

  .platform-overview-card,
  .completion-rate-card,
  .popular-courses-card,
  .top-students-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head-title {
      font-weight: 600;
      color: #1890ff;
    }
  }

  // Overview stat cards
  .overview-stat-card {
    position: relative;
    padding: 24px;
    border-radius: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e8e8e8;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    cursor: default;
    animation: fadeInUp 0.6s ease-out;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);

      &::before {
        opacity: 1;
      }

      .stat-icon {
        transform: scale(1.1) rotate(5deg);
      }

      .stat-value {
        transform: scale(1.05);
      }

      .stat-decoration {
        transform: scale(1.2);
        opacity: 0.8;
      }
    }

    .stat-icon {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      transition: all 0.3s ease;
      z-index: 2;
    }

    .stat-content {
      position: relative;
      z-index: 2;

      .stat-value {
        font-size: 32px;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 8px;
        transition: transform 0.3s ease;
      }

      .stat-label {
        font-size: 14px;
        font-weight: 500;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .stat-decoration {
      position: absolute;
      bottom: -20px;
      right: -20px;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      opacity: 0.1;
      transition: all 0.3s ease;
      z-index: 1;
    }

    // Courses card theme
    &.courses-card {
      border-left: 4px solid #1890ff;

      .stat-icon {
        background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
        color: #1890ff;
      }

      .stat-value {
        color: #1890ff;
      }

      .stat-decoration {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      }
    }

    // Students card theme
    &.students-card {
      border-left: 4px solid #52c41a;

      .stat-icon {
        background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
        color: #52c41a;
      }

      .stat-value {
        color: #52c41a;
      }

      .stat-decoration {
        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      }
    }

    // Sessions card theme
    &.sessions-card {
      border-left: 4px solid #722ed1;

      .stat-icon {
        background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
        color: #722ed1;
      }

      .stat-value {
        color: #722ed1;
      }

      .stat-decoration {
        background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
      }
    }

    // Score card theme
    &.score-card {
      border-left: 4px solid #fa8c16;

      .stat-icon {
        background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
        color: #fa8c16;
      }

      .stat-value {
        color: #fa8c16;
      }

      .stat-decoration {
        background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
      }
    }
  }

  // Custom badge styles for enrollment and sessions
  .enrollment-badge,
  .sessions-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: default;
    position: relative;
    overflow: hidden;
    min-width: 80px;
    justify-content: center;
    animation: fadeInUp 0.6s ease-out;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);

      &::before {
        opacity: 1;
      }

      .badge-icon {
        transform: scale(1.1);
      }

      .badge-number {
        transform: scale(1.05);

        &::after {
          transform: translate(-50%, -50%) scale(1);
        }
      }
    }

    .badge-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      .anticon {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .badge-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;

      .badge-number {
        font-weight: 700;
        font-size: 16px;
        line-height: 1;
        color: #fff;
        transition: all 0.3s ease;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100%;
          height: 100%;
          background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
          border-radius: 50%;
          transform: translate(-50%, -50%) scale(0);
          transition: transform 0.3s ease;
          pointer-events: none;
        }
      }

      .badge-label {
        font-size: 10px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }

  .enrollment-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 1px solid rgba(102, 126, 234, 0.3);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

    &:hover {
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }

    &.high-value {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ffd700 100%);
      animation: pulse 2s infinite;

      .badge-number {
        color: #fff;
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
      }
    }
  }

  .sessions-badge {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: 1px solid rgba(240, 147, 251, 0.3);
    box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);

    &:hover {
      box-shadow: 0 8px 20px rgba(240, 147, 251, 0.4);
    }

    &.high-value {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #00ff88 100%);
      animation: pulse 2s infinite;

      .badge-number {
        color: #fff;
        text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
      }
    }
  }



  // Student table badges - Simplified design
  .score-badge,
  .student-sessions-badge,
  .highest-score-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: default;
    min-width: 70px;
    justify-content: center;
    font-weight: 500;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .badge-icon {
      display: flex;
      align-items: center;
      justify-content: center;

      .anticon {
        font-size: 14px;
      }
    }

    .badge-content {
      display: flex;
      align-items: center;
      gap: 4px;

      .badge-number {
        font-weight: 600;
        font-size: 14px;
        line-height: 1;
      }

      .badge-label {
        font-size: 11px;
        font-weight: 400;
        opacity: 0.8;
      }
    }
  }

  // Score badge variants - Simplified
  .score-badge {
    &.excellent {
      background: #52c41a;
      color: white;
      border: 1px solid #52c41a;

      .badge-icon .anticon {
        color: white;
      }

      &:hover {
        background: #73d13d;
        border-color: #73d13d;
      }
    }

    &.good {
      background: #1890ff;
      color: white;
      border: 1px solid #1890ff;

      .badge-icon .anticon {
        color: white;
      }

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.average {
      background: #fa8c16;
      color: white;
      border: 1px solid #fa8c16;

      .badge-icon .anticon {
        color: white;
      }

      &:hover {
        background: #ffa940;
        border-color: #ffa940;
      }
    }
  }

  // Student sessions badge - Simplified
  .student-sessions-badge {
    background: #722ed1;
    color: white;
    border: 1px solid #722ed1;

    .badge-icon .anticon {
      color: white;
    }

    &:hover {
      background: #9254de;
      border-color: #9254de;
    }

    &.high-value {
      background: #ff4d4f;
      border-color: #ff4d4f;

      &:hover {
        background: #ff7875;
        border-color: #ff7875;
      }
    }
  }

  // Highest score badge - Simplified
  .highest-score-badge {
    background: #13c2c2;
    color: white;
    border: 1px solid #13c2c2;

    .badge-icon .anticon {
      color: white;
    }

    &:hover {
      background: #36cfc9;
      border-color: #36cfc9;
    }

    &.perfect {
      background: #faad14;
      border-color: #faad14;

      &:hover {
        background: #ffc53d;
        border-color: #ffc53d;
      }
    }
  }

  // Responsive design for badges
  @media (max-width: 768px) {
    .enrollment-badge,
    .sessions-badge,
    .score-badge,
    .student-sessions-badge,
    .highest-score-badge {
      padding: 4px 8px;
      min-width: 60px;
      gap: 4px;

      .badge-icon .anticon {
        font-size: 12px;
      }

      .badge-content {
        .badge-number {
          font-size: 12px;
        }

        .badge-label {
          font-size: 10px;
        }
      }

      // Disable hover effects on mobile
      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }

  @media (max-width: 480px) {
    .enrollment-badge,
    .sessions-badge,
    .score-badge,
    .student-sessions-badge,
    .highest-score-badge {
      padding: 3px 6px;
      min-width: 50px;
      gap: 3px;

      .badge-icon .anticon {
        font-size: 11px;
      }

      .badge-content {
        .badge-number {
          font-size: 11px;
        }

        .badge-label {
          font-size: 9px;
        }
      }
    }
  }

  // Styling for both tables (popular courses and top students)
  .popular-courses-card,
  .top-students-card {
    .ant-table-tbody {
      .rank-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        font-weight: 600;

        .anticon {
          font-size: 16px;
        }
      }

      .course-name,
      .student-name {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 0;

        .anticon {
          color: #1890ff;
          font-size: 16px;
        }

        .ant-typography {
          margin: 0;
          font-weight: 500;
        }
      }
    }

    .ant-table-thead {
      .ant-table-cell {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        text-align: center;
      }
    }

    .ant-table-tbody {
      .ant-table-row {
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ant-table-cell {
          border-bottom: 1px solid #f0f0f0;
          padding: 16px;
          vertical-align: middle;
        }
      }
    }

    // Table header styling
    .ant-table-thead > tr > th {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
      border-bottom: 2px solid #dee2e6 !important;
      font-weight: 600 !important;
      color: #495057 !important;
      text-align: center !important;
    }
  }

  .completion-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .completion-details {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .detail-item {
      display: flex;
      align-items: center;
      padding: 20px;
      border-radius: 12px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: currentColor;

        &::before {
          opacity: 1;
        }

        .detail-icon {
          transform: scale(1.1);
        }
      }

      .detail-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 20px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
      }

      .detail-content {
        flex: 1;
        position: relative;
        z-index: 1;

        .detail-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
          font-weight: 500;
        }

        .detail-value {
          font-size: 24px;
          font-weight: 700;
          line-height: 1.2;
        }
      }

      // Total Sessions - Purple theme
      &.sessions-item {
        border-left: 4px solid #722ed1;
        color: #722ed1;

        .detail-icon {
          background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
          color: #722ed1;
        }

        .detail-value {
          color: #722ed1;
        }

        &:hover {
          background: linear-gradient(135deg, #fafafa 0%, #f9f0ff 100%);
        }
      }

      // Completed Sessions - Green theme
      &.completed-sessions-item {
        border-left: 4px solid #52c41a;
        color: #52c41a;

        .detail-icon {
          background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
          color: #52c41a;
        }

        .detail-value {
          color: #52c41a;
        }

        &:hover {
          background: linear-gradient(135deg, #f9fff7 0%, #f6ffed 100%);
        }
      }

      // Completion Rate - Orange theme
      &.rate-item {
        border-left: 4px solid #fa8c16;
        color: #fa8c16;

        .detail-icon {
          background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
          color: #fa8c16;
        }

        .detail-value {
          color: #fa8c16;
        }

        &:hover {
          background: linear-gradient(135deg, #fffbf0 0%, #fff7e6 100%);
        }
      }
    }
  }

  .rank-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .course-name,
  .student-name {
    display: flex;
    align-items: center;
  }

  // Grid layout for cards
  .ant-row {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 16px;

    .statistics-filter-card {
      .form-filter {
        .filter-buttons-col {
          margin-top: 16px;

          .filter-buttons {
            justify-content: stretch;

            .ant-btn {
              flex: 1;
            }
          }
        }

        .filter-info {
          justify-content: center;
          margin-top: 8px;
        }
      }
    }

    .completion-details {
      padding: 16px;
      gap: 12px;

      .detail-item {
        padding: 16px;

        .detail-icon {
          width: 40px;
          height: 40px;
          font-size: 16px;
          margin-right: 12px;
        }

        .detail-content {
          .detail-label {
            font-size: 13px;
          }

          .detail-value {
            font-size: 20px;
          }
        }
      }
    }

    // Stack cards vertically on mobile
    .popular-courses-card,
    .top-students-card {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 576px) {
    .ant-col {
      margin-bottom: 16px;
    }

    .ant-statistic {
      text-align: center;
    }

    .statistics-filter-card {
      .form-filter {
        .filter-buttons {
          flex-direction: column;
          gap: 8px;
        }
      }
    }

    .completion-details {
      padding: 12px;
      gap: 10px;

      .detail-item {
        padding: 12px;
        border-radius: 8px;

        .detail-icon {
          width: 36px;
          height: 36px;
          font-size: 14px;
          margin-right: 10px;
        }

        .detail-content {
          .detail-label {
            font-size: 12px;
          }

          .detail-value {
            font-size: 18px;
          }
        }
      }
    }
  }

  // Table customization
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }

    // Responsive table
    @media (max-width: 768px) {
      .ant-table-content {
        overflow-x: auto;
      }
    }
  }

  // Statistics cards
  .ant-statistic {
    .ant-statistic-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: #666;
    }

    .ant-statistic-content {
      font-weight: 600;
    }
  }

  // Progress circle customization
  .ant-progress-circle {
    .ant-progress-text {
      font-weight: 600;
      font-size: 24px;
    }
  }

  // Tag customization
  .ant-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;

    &.ant-tag-blue {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.ant-tag-green {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.ant-tag-orange {
      background-color: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
  }

  // Card hover effects
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
  }

  // Loading state
  .ant-spin-nested-loading {
    .ant-spin-container {
      transition: opacity 0.3s ease;
    }
  }

  // Empty state
  .ant-empty {
    padding: 40px 20px;

    .ant-empty-description {
      color: #999;
    }
  }
}
